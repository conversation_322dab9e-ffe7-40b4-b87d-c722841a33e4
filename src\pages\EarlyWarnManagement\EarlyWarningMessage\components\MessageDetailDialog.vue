<!--预警消息详情弹窗-->
<template>
  <div>
    <el-dialog
      title="预警消息详情"
      width="900px"
      v-model="state.visible"
      @close="$emit('update:dialogVisible', false)"
    >
      <table class="detail-table">
        <tr>
          <td class="label">预警事件编号</td>
          <td>{{ props.detailData.eventId || '-' }}</td>
          <td class="label">预警规则</td>
          <td>{{ props.detailData.ruleName || '-' }}</td>
        </tr>
        <tr>
          <td class="label">模型编号</td>
          <td>{{ props.detailData.modelCode || '-' }}</td>
          <td class="label">系统名称</td>
          <td>{{ props.detailData.systemName || '-' }}</td>
        </tr>
        <tr>
          <td class="label">预警名称</td>
          <td>{{ props.detailData.modelName || '-' }}</td>
          <td class="label">预警事件时间</td>
          <td>{{ formatDateTime(props.detailData.alertTime) }}</td>
        </tr>
        <tr>
          <td class="label">预警类型</td>
          <td>{{ getAlertTypeLabel(props.detailData.alertType) }}</td>
          <td class="label">是否关联规则</td>
          <td>{{ props.detailData.related ? '是' : '否' }}</td>
        </tr>
        <tr>
          <td class="label">第三方名称</td>
          <td>{{ props.detailData.platformName || '-' }}</td>
          <td class="label">预警频率</td>
          <td>{{ props.detailData.frequency === 0 ? '不限制' : props.detailData.frequency || '-' }}</td>
        </tr>
        <tr>
          <td class="label">期次</td>
          <td>{{ props.detailData.period || '-' }}</td>
          <td class="label">通知方式</td>
          <td>{{ getWarnTypeLabel(props.detailData.warnType) }}</td>
        </tr>
        <tr>
          <td class="label">业务唯一编号</td>
          <td>{{ props.detailData.serviceNo || '-' }}</td>
          <td class="label">通知人员</td>
          <td>
            <el-tooltip
              v-if="props.detailData.notificationUsers && props.detailData.notificationUsers !== '-'"
              class="detail-tooltip"
              placement="top"
              popper-class="custom-detail-tooltip">
              <template #content>
                <div class="detail-tooltip-content">{{ props.detailData.notificationUsers }}</div>
              </template>
              <div class="ellipsis-text">{{ props.detailData.notificationUsers }}</div>
            </el-tooltip>
            <span v-else>-</span>
          </td>
        </tr>
        <tr>
          <td class="label">业务类型</td>
          <td>{{ getBusinessTypeLabel(props.detailData.businessType) }}</td>
          <td class="label">预警级别</td>
          <td>
            <el-tag :type="getWarningLevelType(props.detailData.warnLevel)">
              {{ getWarningLevelLabel(props.detailData.warnLevel) }}
            </el-tag>
          </td>
        </tr>
        <tr>
          <td class="label">状态值</td>
          <td>{{ props.detailData.state || '-' }}</td>
          <td class="label">指标值</td>
          <td>{{ props.detailData.indexValue || '-' }}</td>
        </tr>
        <tr>
          <td class="label">原因</td>
          <td>{{ props.detailData.reason || '-' }}</td>
          <td class="label">拓展</td>
          <td>
            <el-tooltip
              v-if="props.detailData.payload && props.detailData.payload !== '-'"
              class="detail-payload-tooltip"
              placement="top"
              popper-class="custom-detail-payload-tooltip">
              <template #content>
                <div class="detail-payload-tooltip-content">{{ formatPayload(props.detailData.payload) }}</div>
              </template>
              <div class="payload-content ellipsis-text">
                {{ formatPayload(props.detailData.payload) }}
              </div>
            </el-tooltip>
            <span v-else>-</span>
          </td>
        </tr>
      </table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'
import { store } from '@/stores'
import type { AlertMessageDetailVO } from '@/types/alertMessage'

// 参数管理
const props = defineProps<{
  dialogVisible: boolean;
  detailData: AlertMessageDetailVO;
}>();

// 定义要发送的emit事件
let $emit = defineEmits(['update:dialogVisible'])

// 状态管理
const state = reactive({
  visible: false
})

// 根据业务类型值获取显示文本
const getBusinessTypeLabel = (value: string): string => {
  if (!value) return '-'
  const option = store().businessTypeOptions.find(item => item.value === value)
  return option ? option.label : value
}

// 根据预警类型值获取显示文本
const getAlertTypeLabel = (value: string): string => {
  if (!value) return '-'
  const option = store().alertTypeOptions.find(item => item.value === value)
  return option ? option.label : value
}

// 根据预警级别值获取显示文本
const getWarningLevelLabel = (value: string): string => {
  if (!value) return '-'
  const option = store().warnLevelOptions.find(item => item.value === value)
  return option ? option.label : value
}

// 预警级别类型映射
const getWarningLevelType = (level: string): 'danger' | 'warning' | 'success' | 'info' => {
  if (!level) return 'info'
  const label = getWarningLevelLabel(level)
  
  const typeMap: Record<string, 'danger' | 'warning' | 'success' | 'info'> = {
    '高': 'danger',
    '中': 'warning', 
    '低': 'success',
    'high': 'danger',
    'medium': 'warning',
    'low': 'success'
  }
  return typeMap[label] || typeMap[level] || 'info'
}

// 通知方式映射
const getWarnTypeLabel = (type: number): string => {
  const typeMap: Record<number, string> = {
    1: '钉钉',
    2: '短信',
    3: '钉钉+短信'
  }
  return typeMap[type] || '-'
}

// 格式化时间
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return dateTime.replace('T', ' ').substring(0, 19)
}

// 格式化拓展字段
const formatPayload = (payload: string): string => {
  if (!payload || payload === '-') return '-'
  try {
    // 尝试格式化JSON
    const parsed = JSON.parse(payload)
    return JSON.stringify(parsed, null, 2)
  } catch {
    // 如果不是JSON，直接返回原文本
    return payload
  }
}

watch(
  () => props.dialogVisible,
  async (newVal) => {
    state.visible = newVal
  }
)
</script>

<style lang="scss" scoped>
.detail-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
  td {
    height: 30px;
    text-align: center;
    padding: 10px 5px;
    border: 1px solid #E4E4E4;
    word-wrap: break-word;
    word-break: break-all;
    &.label {
      background: #F2F2F2;
      width: 120px;
    }
    &:nth-child(2) {
      width: 30%;
    }
    &:nth-child(4) {
      width: 30%;
    }
  }
}

.payload-content {
  max-height: 200px;
  overflow-y: auto;
  text-align: center;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
}

// 详情页面tooltip样式
:deep(.custom-detail-tooltip) {
  max-width: 50vw !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  white-space: normal !important;
  line-height: 1.4 !important;
  padding: 8px 12px !important;
  font-size: 13px !important;
}

:deep(.custom-detail-payload-tooltip) {
  max-width: 50vw !important;
  word-wrap: break-word !important;
  word-break: break-all !important;
  white-space: pre-wrap !important;
  line-height: 1.5 !important;
  padding: 12px !important;
  font-size: 13px !important;
}

.detail-tooltip-content {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
}

.detail-payload-tooltip-content {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.5;
}

.ellipsis-text {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

:deep(.el-tooltip__trigger) {
  width: 100%;
}
</style>
