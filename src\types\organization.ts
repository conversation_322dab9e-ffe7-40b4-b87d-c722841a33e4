// 组织架构树节点类型定义
export interface OrganizationTreeVO {
  id: number | string         // 对应后端Long类型或String类型
  name: string       // 节点名称
  type: string       // 节点类型 "DEPT" 或 "USER"
  children?: OrganizationTreeVO[] // 子节点

  // 用户特有属性
  phone?: string     // 联系电话
  email?: string     // 邮箱
  userId?: string    // 钉钉用户ID
  post?: string      // 岗位
  mobile?: string    // 手机号
  avatar?: string    // 头像

  // 前端扩展属性
  checked?: boolean  // 选中状态
}