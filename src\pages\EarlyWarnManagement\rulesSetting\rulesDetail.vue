<template>
  <div class="rules-detail-container">
    <!-- 表单内容 -->
    <el-form :model="formData" :rules="rules" ref="formRef" label-width="200px" class="rules-form">
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">
          <div class="title-bar"></div>
          <span>基本信息</span>
        </div>
        <el-form-item label="规则名称:" prop="ruleName" required>
          <el-input
            v-model="formData.ruleName"
            placeholder="请输入"
            :readonly="isViewMode"
            maxlength="100"
            show-word-limit
            type="text" />
        </el-form-item>
        <el-form-item label="规则状态:" prop="ruleStatus" required>
          <el-switch
            v-model="formData.ruleStatus"
            active-value="0"
            inactive-value="1"
            :disabled="isViewMode" />
        </el-form-item>
      </div>
      <!-- 预警范围 -->
      <div class="form-section">
        <div class="section-title">
          <div class="title-bar"></div>
          <span>预警范围</span>
        </div>
        <el-form-item label="设置预警条件:" prop="conditionList" required>
          <div class="conditions-container">
            <div class="conditions-header">
              <el-select
                v-model="formData.ruleMatching"
                style="width: 150px; margin-right: 300px"
                :disabled="isViewMode">
                <el-option label="满足所有条件" value="1" />
                <el-option label="满足任一条件" value="0" />
              </el-select>
              <el-button type="primary" @click="addCondition" v-if="!isViewMode">
                <el-icon><Plus /></el-icon>
                添加条件
              </el-button>
            </div>
            <div class="conditions-list">
              <div
                v-for="(condition, index) in formData.conditionList"
                :key="index"
                class="condition-item">
                <div class="condition-content">
                  <el-form-item
                    :prop="`conditionList.${index}.settingItem`"
                    :rules="conditionRules.settingItem">
                    <el-select
                      v-model="condition.settingItem"
                      placeholder="请选择字段"
                      style="width: 200px"
                      :disabled="isViewMode">
                      <el-option
                        v-for="item in loanFailedOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value" />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :prop="`conditionList.${index}.operator`"
                    :rules="conditionRules.operator">
                    <el-select
                      v-model="condition.operator"
                      placeholder="请选择操作符"
                      style="width: 120px"
                      :disabled="isViewMode">
                      <el-option
                        v-for="item in operatorOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value" />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :prop="`conditionList.${index}.assignmentItem`"
                    :rules="conditionRules.assignmentItem">
                    <el-input
                      v-model="condition.assignmentItem"
                      placeholder="请输入值"
                      style="width: 150px"
                      :disabled="isViewMode"
                      type="number" />
                  </el-form-item>
                </div>
                <el-button
                  type="danger"
                  @click="removeCondition(index)"
                  size="small"
                  circle
                  v-if="!isViewMode">
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </el-form-item>
      </div>

      <!-- 应用资方 -->
      <div class="form-section">
        <div class="section-title">
          <div class="title-bar"></div>
          <span>应用资方</span>
        </div>

        <el-form-item label="选择资方:" prop="fundProviders">
          <div class="providers-container">
            <div class="providers-header">
              <el-checkbox
                v-model="selectAll"
                :indeterminate="isIndeterminate"
                :disabled="isViewMode">
                全选
              </el-checkbox>
            </div>
            <div class="providers-list">
              <el-checkbox-group v-model="formData.fundProviders" :disabled="isViewMode">
                <div class="providers-grid">
                  <el-checkbox
                    v-for="provider in fundProviderOptions"
                    :key="provider.id"
                    :value="provider.id"
                    class="provider-item">
                    {{ provider.capitalName }}
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </el-form-item>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSubmit" v-if="!isViewMode">确定</el-button>
        <el-button @click="goBack">{{ isViewMode ? '返回' : '取消' }}</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Close } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import type { FormInstance, FormRules } from 'element-plus'
import RulesSetting from '@/api/RulesSetting'
import SysDict from '@/api/DataManagement'
import Capital from '@/api/capital'
const router = useRouter()
const route = useRoute()

const formRef = ref<FormInstance>()

// 判断是否为查看模式
const isViewMode = computed(() => {
  return route.query.mode === 'view'
})
// 表单数据
const formData = reactive({
  ruleName: '',
  ruleStatus: '0',
  ruleMatching: '1',
  conditionList: [],
  fundProviders: [] as string[]
})
//运算符选项
const operatorOptions = ref([])
const getOperatorOptions = async () => {
  const res = await SysDict.getSysDictValuePage({
    keyName: 'operator'
  })
  operatorOptions.value = res.data.records
}
//放款失败预警条件选项
const loanFailedOptions = ref([])
const getLoanFailedOptions = async () => {
  const res = await SysDict.getSysDictValuePage({
    keyName: 'fangkuan'
  })
  loanFailedOptions.value = res.data.records
}
// 资方选项
const fundProviderOptions = ref([])
const getFundProviderOptions = async () => {
  const res = await Capital.getCapitalList()
  fundProviderOptions.value = res.data.records
}

// 条件验证规则
const conditionRules = {
  settingItem: [{ required: true, message: '请选择字段', trigger: 'change' }],
  operator: [{ required: true, message: '请选择操作符', trigger: 'change' }],
  assignmentItem: [{ required: true, message: '请输入值', trigger: 'blur' }]
}

// 表单验证规则
const rules: FormRules = {
  ruleName: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { max: 100, message: '规则名称最多输入100个字符', trigger: 'blur' }
  ],
  ruleStatus: [{ required: true, message: '请选择规则状态', trigger: 'change' }],
  conditionList: [
    {
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error('请至少添加一条预警条件'))
          return
        }
        callback()
      },
      trigger: ['blur', 'change']
    }
  ]
}

// 获取可选的预警规则设置项（排除已选择的预警规则设置项）
const getAvailableOptions = (currentIndex: number) => {
  const selectedItems = formData.conditionList
    .map((condition, index) => ({ value: condition.settingItem, index }))
    .filter((item) => item.value && item.index !== currentIndex)
    .map((item) => item.value)

  return loanFailedOptions.value.filter((option) => !selectedItems.includes(option.value))
}

// 添加条件
const addCondition = () => {
  if (formData.conditionList.length >= 5) {
    ElMessage.error('最多添加5个条件')
    return
  }
  formData.conditionList.push({
    id: null,
    settingItem: '',
    operator: '',
    assignmentItem: ''
  })
  // 重新验证条件列表
  nextTick(() => {
    if (formRef.value) {
      formRef.value.validateField('conditionList')
    }
  })
}
// 删除条件
const removeCondition = (index: number) => {
  formData.conditionList.splice(index, 1)
  // 重新验证条件列表
  nextTick(() => {
    if (formRef.value) {
      formRef.value.validateField('conditionList')
    }
  })
}
// 全选相关
const selectAll = computed({
  get() {
    return (
      formData.fundProviders.length > 0 &&
      formData.fundProviders.length === fundProviderOptions.value.length
    )
  },
  set(value: boolean) {
    if (value) {
      formData.fundProviders = fundProviderOptions.value.map((item) => item.id)
    } else {
      formData.fundProviders = []
    }
  }
})

const isIndeterminate = computed(() => {
  const checkedCount = formData.fundProviders.length
  return checkedCount > 0 && checkedCount < fundProviderOptions.value.length
})

// 返回
const goBack = () => {
  router.back()
}
// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  try {
    await formRef.value.validate()
    const params = {
      ...formData,
      applyInvestor: formData.fundProviders.join(',')
    }
    const id = route.params.id
    let response
    if (id) {
      // 更新规则
      response = await RulesSetting.updateRule({ ...params, id })
    } else {
      // 创建规则
      response = await RulesSetting.createRule(params)
    }
    if (response.code === '0000') {
      ElMessage.success('保存成功')
      router.back()
    } else {
      ElMessage.error(response.msg || response.message || '保存失败')
    }
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 页面加载
onMounted(() => {
  // 如果有ID参数，加载现有数据
  const id = route.params.id
  if (id) {
    loadRuleDetail(id as string)
  } else if (!isViewMode.value) {
    addCondition()
  }
  getOperatorOptions()
  getLoanFailedOptions()
  getFundProviderOptions()
})

// 加载规则详情
const loadRuleDetail = async (id: string) => {
  try {
    const response = await RulesSetting.getRuleDetail(id)
    if (response.code === 0 || response.code === '0000') {
      const data = response.data || response.result
      // 更新表单数据
      Object.assign(formData, {
        ruleName: data.ruleName || '',
        ruleStatus: data.ruleStatus,
        ruleMatching: data.ruleMatching,
        warnLevel: data.warnLevel,
        conditionList: data.conditionList || [],
        fundProviders: data.applyInvestor
          ? data.applyInvestor
              .split(',')
              .filter((item) => item.trim() !== '')
              .map(Number)
          : []
      })
      await nextTick()
    } else {
      ElMessage.error(response.msg || response.message || '加载规则详情失败')
    }
  } catch (error) {
    console.error('加载规则详情失败:', error)
    ElMessage.error('加载规则详情失败')
  }
}
</script>

<style scoped lang="scss">
.rules-detail-container {
  padding: 20px;
  background: #fff;
  height: 100%;
  overflow-y: auto;
}

.rules-form {
  .form-section {
    margin-bottom: 30px;
    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      .title-bar {
        width: 4px;
        height: 20px;
        background: #409eff;
        margin-right: 10px;
        border-radius: 2px;
      }
      span {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
}
.conditions-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  background: #fff;

  .conditions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .conditions-list {
    .condition-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      .condition-content {
        display: flex;
        gap: 10px;
        flex: 1;
        align-items: flex-start;
      }
    }
  }
}

.providers-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  .providers-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e4e7ed;
  }
  .providers-list {
    max-height: 300px;
    max-width: 800px;
    overflow-y: auto;
    overflow-x: hidden;
    .providers-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      .provider-item {
        margin-bottom: 10px;
      }
    }
  }
}

.form-actions {
  text-align: center;
  margin-top: 40px;
  padding-top: 20px;
}
</style>
