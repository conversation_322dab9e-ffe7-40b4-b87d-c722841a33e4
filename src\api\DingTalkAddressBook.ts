import { sysApi } from '@/globalSettings'
import http from '@/assets/js/http'

export default {
  // 钉钉通讯录-获取部门
  getDept: function(params: object) {
    return http.get(`${sysApi}/dingTalk/addressBook/dept`, params)
  },
  // 钉钉通讯录-获取用户
  getUser: function(params: object) {
    return http.get(`${sysApi}/dingTalk/addressBook/user/page`, params)
  },
  // 钉钉通讯录-同步数据
  syncData: function(params: object) {
    return http.post(`${sysApi}/dingTalk/addressBook/sync`, params)
  }
}
