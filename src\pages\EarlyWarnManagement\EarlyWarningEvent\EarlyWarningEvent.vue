<!--预警模型-->
<template>
  <div>
    <el-form :inline="true" :model="search" ref="searchFormRef">
      <el-form-item label="预警事件编号：" prop="eventId">
        <el-input v-model="search.eventId" placeholder="请输入" maxlength="100" />
      </el-form-item>
      <el-form-item label="业务唯一编号：" prop="serviceNo">
        <el-input v-model="search.serviceNo" placeholder="请输入" maxlength="100" />
      </el-form-item>
      <el-form-item label="第三方名称：" prop="platformName">
        <el-input v-model="search.platformName" placeholder="请输入" maxlength="100" />
      </el-form-item>
      <el-form-item label="预警名称：" prop="modelName">
        <el-select v-model="search.modelName" filterable allow-create default-first-option placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in (store() as any).modelNameOptions"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型：" prop="businessType">
        <el-select v-model="search.businessType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in (store() as any).businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="上报时间：" prop="createTime">
        <el-date-picker
          v-model="search.createTime"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="预警类型：" prop="alertType">
        <el-select v-model="search.alertType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in (store() as any).alertTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态值：" prop="state">
        <el-select v-model="search.state" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in warnStatesData"
            :key="item.value"
            :label="item.name"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="系统名称：" prop="systemName">
        <el-select v-model="search.systemName" filterable allow-create default-first-option placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in (store() as any).systemNameOptions"
            :key="item"
            :label="item"
            :value="item" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button @click="resetForm(searchFormRef)">重置</el-button>
        <el-button :icon="Download" @click="exportRecord">下载</el-button>
      </el-form-item>
    </el-form>
    <el-table @selection-change="handleSelectionChange" :data="tableData" border stripe v-loading="loading" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="eventId" label="预警事件编号" width="185"/>
      <el-table-column prop="modelCode" label="模型编号" width="85"/>
      <el-table-column prop="alertType" label="预警类型" width="85"/>
      <el-table-column prop="platformName" label="第三方名称" width="130"/>
      <el-table-column prop="period" label="期次" width="55"/>
      <el-table-column prop="serviceNo" label="业务唯一编号"/>
      <el-table-column prop="businessType" label="业务类型" width="85"/>
      <el-table-column prop="state" label="状态值" width="75"/>
      <el-table-column prop="indexValue" label="指标值" width="75"/>
      <el-table-column prop="reason" label="原因" width="200">
        <template #default="{ row }">
          <el-tooltip
            class="box-item"
            :content="row.reason"
            placement="top">
            <p class="ellipsis">{{ row.reason }}</p>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="modelName" label="预警名称" width="90"/>
      <el-table-column prop="systemName" label="系统名称" width="90"/>
      <el-table-column prop="createTime" label="上报时间" width="110"/>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button type="primary" text @click="detailDialog(row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="mt20 flex_row flex_center">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[5, 10, 20, 50, 100]"
        :background="true"
        layout="prev, pager, next, jumper, sizes, total"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <WarnDetailDialog v-model:dialogVisible="warnDetailDialog.show" :detailData="warnDetailDialog.detailData"></WarnDetailDialog>
  </div>
</template>

<script setup lang="ts">
import {onMounted, reactive, ref} from 'vue'
import {ElMessage} from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import WarnDetailDialog from './components/WarnDetailDialog.vue'
import type { FormInstance } from 'element-plus'
import WarnApi from '@/api/EarlyWarnManage'
import { warnStatesData } from '@/config/config'
import { store } from '@/stores'
import http from '@/assets/js/http'
import { sysApi } from '@/globalSettings'

// 搜索数据
const search = reactive({
  eventId: '', // 预警事件编号
  serviceNo: '', // 业务唯一编号
  platformName: '', // 第三方名称
  modelName: '', // 预警名称
  businessType: '', // 业务类型
  createTime: [], // 上报时间
  alertType: '', // 预警类型
  state: '', // 状态值
  systemName: '' // 系统名称
})

// 表格
let tableData = reactive([{code: 1}])
const loading = ref(false)

let pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const disabledDate = (time: any) => {
  // 禁用传入的日期早于今天
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return time > today
}

// 表格选择
const selectableIds = ref([])
const handleSelectionChange = (val: any) => {
  selectableIds.value = val.map((item: any) => item.id)
}

// 导出
const exportRecord = () => {
  if (selectableIds.value.length === 0) {
    ElMessage({ message: '请勾选需要导出的数据', type: 'warning' })
    return
  }
  http.fileBlob(
    `${sysApi}/alertEvent/export`,
    {ids: selectableIds.value}, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    true,
    'POST')
}

// 获取数据
const getList = (page = 1) => {
  let params = {
    pageNum: page || 1,
    pageSize: pagination.pageSize,
    eventId: search.eventId, // 预警事件编号
    serviceNo: search.serviceNo, // 业务唯一编号
    platformName: search.platformName, // 第三方名称
    modelName: search.modelName, // 预警名称
    businessType: search.businessType, // 业务类型
    alertType: search.alertType, // 预警类型
    state: search.state, // 状态值
    systemName: search.systemName, // 系统名称
    startTime: search.createTime?.[0], // 上报时间
    endTime: search.createTime?.[1] // 上报时间
  }
  loading.value = true
  WarnApi.getAlertEvent(params).then((res: any) => {
    loading.value = false
    const { code, data, message } = res || {}
    if (code === '0000') {
      tableData = data.records
      pagination.total = Number(data.total)
      pagination.currentPage = Number(data.current)
    } else {
      ElMessage({ message: message, type: 'error' })
    }
  })
}

// 清除过滤条件
const searchFormRef = ref<FormInstance>()
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getList(1)
}
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  getList(val)
}

// 详情弹窗
const warnDetailDialog = reactive({
  show: false,
  detailData: {}
})

const detailDialog = (row: any) => {
  warnDetailDialog.show = true
  warnDetailDialog.detailData = row
}

onMounted(async () => {
  await store().allSystemName()
  await store().allModelName()
  await store().allBusinessType()
  await store().allAlertType()
  getList(1)
})
</script>

<style lang="scss" scoped>
.el-input {
  width: 220px;
}
.el-select {
  width: 220px;
}
.el-message-box__container {
  align-items: normal;
}
.ellipsis {
  width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
:deep(.box-item) {
  width: 600px;
  font-size: 29px;
}
</style>
