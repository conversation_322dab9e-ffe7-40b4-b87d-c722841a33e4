<!--API数据源-->
<template>
  <div>
    <el-form :inline="true" :model="searchForm" ref="searchFormRef">
      <el-form-item label="系统名称:" prop="systemName">
        <el-input v-model="searchForm.systemName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="系统编号:" prop="systemCode">
        <el-input v-model="searchForm.systemCode" placeholder="请输入" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button @click="resetForm(searchFormRef)">重置</el-button>
      </el-form-item>
    </el-form>
    <el-button class="mb10" type="primary" @click="openDialog({}, 1)">添加</el-button>
    <el-table :data="tableData" border stripe v-loading="loading" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }">
      <el-table-column type="index" label="数据源编号" width="100" />
      <el-table-column prop="systemName" label="系统名称" />
      <el-table-column prop="systemCode" label="系统编号" />
      <el-table-column prop="description" label="备注" />
      <el-table-column prop="updateTime" label="最后更新时间" width="170" />
      <el-table-column label="操作" width="260">
        <template #default="{ row }">
          <el-button type="primary" text @click="openDialog(row, 3)">查看配置</el-button>
          <el-button type="primary" text @click="openDialog(row, 2)">修改</el-button>
          <el-button type="danger" text @click="deleteData(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="mt20 flex_row flex_center">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[5, 10, 20, 50, 100]"
        :background="true"
        layout="prev, pager, next, jumper, sizes, total"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      :title="state.type === 1 ? '添加数据源' : '修改数据源'"
      @close="resetDialog(ruleFormRef)"
      v-model="state.visible"
      width="500px">
      <el-form :model="state.form" :rules="rules" ref="ruleFormRef" label-width="120" :inline="true">
        <el-form-item label="系统名称" prop="systemName">
          <el-input v-model="state.form.systemName" maxlength="20" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="系统编号" prop="systemCode">
          <el-input v-model="state.form.systemCode" maxlength="20" placeholder="请输入" :disabled="state.type === 2"/>
        </el-form-item>
        <el-form-item label="备注描述" prop="description">
          <el-input class="w260" v-model="state.form.description" maxlength="100" type="textarea" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div class="tc mt20">
        <el-button type="primary" @click="saveDialog(ruleFormRef)" :loading="state.btnLoading">保存</el-button>
        <el-button @click="resetDialog(ruleFormRef)">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="数据源信息" width="400px" v-model="state.visibleMsg">
      <el-row><el-col :span="6">系统名称：</el-col><el-col :span="16">{{ state.form.systemName }}</el-col></el-row>
      <el-row><el-col :span="6">系统编号：</el-col><el-col :span="16">{{ state.form.systemCode }}</el-col></el-row>
      <el-row><el-col :span="6">描述：</el-col><el-col :span="16">{{ state.form.description }}</el-col></el-row>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="state.visibleMsg = false">知道了</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormRules, FormInstance } from 'element-plus'
import WarnApi from '@/api/EarlyWarnManage'

// 搜索数据
const searchForm = reactive({
  systemName: '', // 系统名称
  systemCode: '' // 系统编号
})

// 表格
let tableData = reactive([])
const loading = ref(false)

let pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 获取数据
const getList = (page = 1) => {
  let params = {
    pageNum: page || 1,
    pageSize: pagination.pageSize,
    systemName: searchForm.systemName,
    systemCode: searchForm.systemCode
  }
  loading.value = true
  WarnApi.getDataSource(params).then((res: any) => {
    loading.value = false
    const { code, data, message } = res || {}
    if (code === '0000') {
      tableData = data.records
      pagination.total = Number(data.total)
      pagination.currentPage = Number(data.current)
    } else {
      ElMessage({ message: message, type: 'error' })
    }
  })
}

// 清除过滤条件
const searchFormRef = ref<FormInstance>()
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getList(1)
}
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  getList(val)
}

// 删除
const deleteData = (id: string) => {
  ElMessageBox.confirm('确定要删除数据源吗？', {
    title: '删除提示',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    WarnApi.delDataSource({ id }).then((res: any) => {
      if (res.code === '0000') {
        getList(1)
        ElMessage({ message: '删除成功', type: 'success' })
      } else {
        ElMessage({ message: res.message, type: 'error' })
      }
    })
  })
}

// 弹窗-状态管理
const state = reactive({
  visible: false,
  visibleMsg: false,
  type: 1,
  form: {
    id: '',
    sourceType: 'api',
    systemName: '',
    systemCode: '',
    description: ''
  },
  btnLoading: false
})

// 编辑添加查看弹窗
const openDialog = (row: any, type: number) => {
  type === 3 ? (state.visibleMsg = true) : (state.visible = true)
  state.form = { ...row }
  state.type = type
}

const rules = reactive<FormRules>({
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z\u4e00-\u9fa5]+$/, message: '请输入中文或字母', trigger: 'blur' }
  ],
  systemCode: [
    { required: true, message: '请输入系统编号', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9]+$/, message: '请输入数字或字母', trigger: 'blur' }
  ]
})

// 重置弹窗
const resetDialog = (formEl: FormInstance | undefined) => {
  state.visible = false
  if (!formEl) return
  formEl.resetFields()
}

const ruleFormRef = ref<FormInstance>()

// 弹窗保存
const saveDialog = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      state.btnLoading = true
      let params = {
        sourceType: 'api',
        systemName: state.form.systemName,
        systemCode: state.form.systemCode,
        description: state.form.description
      }
      if (state.type === 2) {
        params.id = state.form.id
      }
      let apiName = 'saveDataSource'
      if (state.form.id) {
        apiName = 'updateDataSource'
      }
      (WarnApi as any)[apiName](params).then((res: any) => {
        state.btnLoading = false
        if (res.code === '0000') {
          state.visible = false
          getList(1)
          ElMessage({ type: 'success', message: '编辑成功' })
        } else {
          ElMessage({ type: 'error', message: res.message })
        }
      })
    }
  })
}

onMounted(() => {
  getList(1)
})
</script>

<style lang="scss" scoped>
.el-input {
  width: 220px;
}
.el-message-box__container {
  align-items: normal;
}
.w260 {
  width: 260px;
}
.el-row {
  margin-bottom: 10px;
}
.el-row .el-col:first-child {
  text-align: right;
}
</style>
