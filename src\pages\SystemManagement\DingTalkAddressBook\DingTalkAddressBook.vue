<!--通讯录-->
<template>
  <div class="contact-container">
    <!-- 顶部查询栏 -->
    <div class="search-bar">
      <span class="search-label">查询：</span>
      <el-input
        v-model="search.nameOrMobile"
        placeholder="请输入姓名/手机号"
        style="width: 300px; margin-right: 15px;"
        clearable
      />
      <el-button type="primary" @click="handleSearch">查询</el-button>

      <div class="sync-info">
        <span>上次同步时间: {{ lastSyncTime }}</span>
        <el-button
          type="primary"
          plain
          :loading="syncLoading"
          @click="syncContacts"
          style="margin-left: 15px;"
        >
          {{ syncLoading ? '同步中...' : '同步通讯录' }}
        </el-button>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="main-content">
      <!-- 左侧部门树 -->
      <div class="left-panel">
        <el-card shadow="hover">
          <template #header>
            <div class="panel-header">
              <span>组织结构</span>
            </div>
          </template>

          <el-tree
            :data="orgTree"
            :props="treeProps"
            :highlight-current="true"
            node-key="id"
            :default-expanded-keys="defaultExpandedKeys"
            :expand-on-click-node="false"
            ref="treeRef"
            @node-click="handleNodeClick"
          >
            <template #default="{ node }">
              <span class="tree-node">
                <el-icon v-if="node.level === 1">
                  <OfficeBuilding />
                </el-icon>
                <el-icon v-else>
                  <Folder />
                </el-icon>
                <span>{{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </el-card>
      </div>

      <!-- 右侧通讯录表格 -->
      <div class="right-panel">
        <el-table v-loading="loading" :data="tableData" style="width: 100%" height="calc(100vh - 370px)" stripe
                  border>
          <el-table-column prop="name" label="姓名" min-width="200" align="center" />
          <el-table-column prop="post" label="岗位" min-width="200" align="center" />
          <el-table-column prop="deptName" label="部门" min-width="200" align="center" />
          <el-table-column prop="mobile" label="手机号" min-width="200" align="center" />
          <el-table-column prop="email" label="邮箱" min-width="300" align="center" />
        </el-table>

        <div class="mt20 flex_row flex_center">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[5, 10, 20, 50, 100]"
            :background="true"
            layout="prev, pager, next, jumper, sizes, total"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import api from '@/api/DingTalkAddressBook'
import { ref, onMounted, reactive, nextTick } from 'vue'
import { OfficeBuilding, Folder } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { store } from '@/stores'
import { ElTree } from 'element-plus' // 引入组件类型

const loading = ref(false)
// 添加同步操作的加载状态（单独控制）
const syncLoading = ref(false)
// 获取树实例
const treeRef = ref<InstanceType<typeof ElTree> | null>(null)
interface TreeNode {
  id: any;
  label: any;
  children?: TreeNode[]; // 可选子节点
}
const treeProps = {
  children: 'children',
  label: 'label'
}
// 组织结构树（完全按图片结构调整）
const orgTree = ref<TreeNode[]>([])
// 默认展开的节点
const defaultExpandedKeys = ref<(number | string)[]>([])
// 转换函数（递归）
const convertToTree = (nodes: any[]): TreeNode[] => {
  return nodes.map((node: any): TreeNode => {
    return {
      id: node.deptId,
      label: node.deptName,
      children: node.subList ? convertToTree(node.subList) : []
    }
  })
}
// 获取部门
const getDept = () => {
  loading.value = true
  api.getDept({}).then((res: any) => {
    loading.value = false
    const { code, data, message } = res || {}
    if (code === '0000') {
      orgTree.value = convertToTree(data)
      if (data.length > 0) {
        defaultExpandedKeys.value = [orgTree.value[0].id]
        lastSyncTime.value = data[0].updateTime
      }
      search.deptId = ''
      search.nameOrMobile = ''
      if (treeRef.value?.setCurrentKey) {
        treeRef.value.setCurrentKey(undefined)
      }
    } else {
      ElMessage({ message: message, type: 'error' })
    }
  })
}

// 搜索数据
const search = reactive({
  deptId: '', // 部门id
  nameOrMobile: '' // 手机号
})

// 最后更新时间
const lastSyncTime = ref('')

// 表格
let tableData = reactive([])
let pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
// 获取用户
const getUser = (page = 1) => {
  let params = {
    pageNum: page || 1,
    pageSize: pagination.pageSize,
    deptId: search.deptId,
    nameOrMobile: search.nameOrMobile
  }
  loading.value = true
  api.getUser(params).then((res: any) => {
    loading.value = false
    const { code, data, message } = res || {}
    if (code === '0000') {
      tableData = data.records
      pagination.total = Number(data.total)
      pagination.currentPage = Number(data.current)
    } else {
      ElMessage({ message: message, type: 'error' })
    }
  })
}

const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getUser(1)
}
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  getUser(val)
}
// 处理部门节点点击
const handleNodeClick = (data: any) => {
  search.nameOrMobile = ''
  search.deptId= data.id
  pagination.currentPage = 1
  getUser(1)
}

// 处理查询
const handleSearch = () => {
  pagination.currentPage = 1
  search.deptId = ''
  getUser(1)
  nextTick(() => {
    if (treeRef.value?.setCurrentKey) {
      treeRef.value.setCurrentKey(undefined)
    }
  })
}



// 同步通讯录
// 同步通讯录 - 优化版
const syncContacts = async () => {
  try {
    syncLoading.value = true // 开启同步按钮的加载状态
    ElMessage.warning('通讯录同步中，请勿关闭该界面...')
    const res = await api.syncData({})
    const { code, message } = res || {}
    if (code === '0000') {
      // 更新最后同步时间
      lastSyncTime.value = new Date().toLocaleString()
      await Promise.all([getDept()]);
      tableData = []
      ElMessage.success('通讯录同步成功！')
    } else {
      ElMessage.error(message || '同步失败')
    }
  } catch (error) {
    const message = (error as Error)?.message || '未知错误';
    ElMessage.error(`同步失败: ${message}`);
  } finally {
    syncLoading.value = false // 关闭加载状态
  }
}

onMounted(async () => {
  await store().allBusinessType()
  await store().allAlertType()
  getDept()
})
</script>

<style scoped>
.contact-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  padding: 20px;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.sync-info {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 20px;
}

.left-panel {
  width: 280px;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.panel-header {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.tree-node {
  display: flex;
  align-items: center;
  font-size: 14px;
  gap: 8px;
}

.pagination {
  padding: 15px;
  display: flex;
  justify-content: center;
  background: white;
  border-top: 1px solid #ebeef5;
}
</style>
