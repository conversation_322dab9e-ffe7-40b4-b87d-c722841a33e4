<!-- 菜单组件 -->
<template>
  <div v-loading="state.menuLoading">
    <el-scrollbar wrap-class="nav-left-scroll">
      <div class="nav-menu-box" :class="{ 'nav-menu-box-horizontal': props.mode === 'horizontal' }">
        <div class="logo_box flex_row flex_center" v-on:click="linkHome()">
          <img v-if="state.isCollapse" class="logo" src="../../assets/imgs/head-icon.png" alt="logo" />
<!--          <p class="project_name" v-if="!state.isCollapse">{{sysName}}</p>-->
          <p class="project_name" v-else><img src="../../assets/imgs/nav_logo.png" alt=""></p>
        </div>
        <!-- S横向配置 占位符-->
        <!-- <div :class="{ 'flex-grow': props.mode === 'horizontal' }" /> -->
        <!-- E横向配置 占位符-->
        <el-menu
          class="nav-menu"
          :class="{ 'ml50': props.mode === 'horizontal' }"
          :default-active="state.currentPath"
          :collapse="state.isCollapse"
          router
          unique-opened
          :mode="props.mode"
          :ellipsis="false"
          :collapse-transition="false"
          :background-color="state.menuBackgroundColor"
          :text-color="state.menuTextColor"
          :active-text-color="state.menuActiveTextColor"
        >
          <template v-for="(item, index) in state.menuList">
            <!-- 一级 菜单权限并且携带url可跳转 -->
            <template v-if="item.rightType === 1 && item.rightUrl && item.treeData.length === 0 && MenuController(item)">
              <el-menu-item :key="item.id" :index="item.rightUrl">
                <span class="ml5 menu-name-1" :title="item.rightName">{{ item.rightName }}</span>
              </el-menu-item>
            </template>
            <!-- 二级菜单 -->
            <template v-else>
              <el-sub-menu :index="index + ''" :key="index + ''" popper-class="nav-menu_popper">
                <!-- 二级菜单标题 -->
                <template #title>
                  <el-icon :size="20">
                    <component :is="iconRightNameMap[item.rightUrl]" />
                  </el-icon>
                  <span class="ml5 menu-name-1" :title="item.rightName">{{ item.rightName }}</span>
                </template>
                <!-- 处理二级菜单的子项 -->
                <template v-for="itemChild in item.treeData">
                  <!-- 如果存在三级菜单：判断如果类似是按钮 并且 treeData长度 > 0 -->
                  <template v-if="itemChild.rightType === 2 && itemChild.treeData && itemChild.treeData.length > 0">
                    <el-sub-menu :index="itemChild.id + ''" :key="itemChild.id + ''" v-if="MenuController(itemChild)">
                      <template #title>
                        <span class="menu-name-1-1" :title="itemChild.rightName">{{itemChild.rightName}}</span>
                      </template>
                      <!-- 三级菜单的子项 -->
                      <template v-for="itemGrandChild in itemChild.treeData" :key="itemGrandChild.id">
                        <el-menu-item :index="itemGrandChild.rightUrl" v-if="MenuController(itemGrandChild)">
                          <template v-if="itemGrandChild.rightType === 1">
                            <span class="menu-name-1-1-1" :title="itemGrandChild.rightName">{{itemGrandChild.rightName}}</span>
                          </template>
                        </el-menu-item>
                      </template>
                    </el-sub-menu>
                  </template>
                  <!-- 如果没有三级菜单，正常显示二级菜单的子项 -->
                  <template v-else-if="MenuController(itemChild)">
                    <el-menu-item :key="itemChild.id" :index="itemChild.rightUrl">
                    <template v-if="itemChild.rightType === 1">
                      <span class="menu-name-1-1" style="margin-left: 13px" :title="itemChild.rightName">{{itemChild.rightName}}</span>
                    </template>
                    </el-menu-item>
                  </template>
                </template>
              </el-sub-menu>
            </template>
          </template>
        </el-menu>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { sysName } from '@/globalSettings'
import { menuStore } from '@/stores/menuStore'
import { themeStore } from '@/stores/theme'
import { reactive, onMounted, computed, watch, shallowRef } from 'vue'
import { DataLine, Monitor, Bell } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import utils from '@/assets/js/utils'
import themes from '@/assets/css/theme/themes'
const router = useRouter()
// 参数管理
const props = defineProps(['mode'])

// 菜单状态管理
const MenuStoreId = menuStore()
const themeStoreId = themeStore()

// 声明初始对象类型
interface Menu {
  id: string
  rightType: number
  rightUrl: string
  rightName: string
  treeData: Menu[]
}

// 状态管理工具
const state = reactive({
  menuLoading: false,
  currentPath: '',
  menuList: [] as Menu[],
  routesList: router.getRoutes(),
  // 数组转字符串的权限树
  menuPaths: '',
  isCollapse: computed(() => {
    return MenuStoreId.isHideFullNavMenu
  }),
  menuBackgroundColor: computed(() => {
    return themes[themeStoreId.themeName]['menuBackgroundColor']
  }),
  menuTextColor: computed(() => {
    return themes[themeStoreId.themeName]['menuTextColor']
  }),
  menuActiveTextColor: computed(() => {
    return themes[themeStoreId.themeName]['menuActiveTextColor']
  })
})

interface iconMap {
  [key: string]: any
}

// 匹配路由图标
const iconRightNameMap = shallowRef({
    '/DataSouceManagement': DataLine,
    '/EarlyWarnManagement': Bell,
    '/SystemManagement': Monitor
}) as iconMap

// 菜单显示/隐藏控制器
const MenuController = (item: any) => {
  if (!item.rightUrl) {
    return true
  }
  const _routes = state.routesList
  for (let i = 0; i < _routes.length; i++) {
    if (item.rightUrl === _routes[i].path) {
      return _routes[i].meta.isShow
    }
  }
}

// 跳转到首页
const linkHome = () => {
  router.push('/home')
}

// 获取菜单配置
const MenuPathInit = (_data: any) => {
  const _menu = _data
  let menuPath = []
  // 储存权限树存在的前端路由
  const rightUrls: any[] = []
  // 广度遍历权限树
  function BFS(tree: any[]) {
    const arr: any[] = []
    tree.forEach((item: { rightUrl: any; treeData: any }, i: number) => {
      if (item.rightUrl) {
        rightUrls.push(item.rightUrl)
      }
      // 将数组拆分成元素Push进新数组
      if (item.treeData) {
        arr.push(...item.treeData)
      }
      // 当前数组遍历完毕
      if (i === tree.length - 1) {
        // 如果没有子类，则跳出循环
        if (!arr.length) return rightUrls
        // 循环递归
        BFS(arr)
      }
    })
    return rightUrls
  }
  menuPath = BFS(_menu)
  return String(menuPath)
}
// 菜单初始化
const getMenuData = () => {
  state.menuList = utils.getLocalStorage('UserPowerTreeData')
  state.menuPaths = MenuPathInit(state.menuList)
  utils.setLocalStorage('UserPermissions', state.menuPaths) // 登记用户权限
  state.currentPath = router.currentRoute.value.path
}
// 监听路由变化
watch(
  () => router.currentRoute.value,
  (to: any) => {
    state.currentPath = to.path
  },
  { immediate: true }
)
// 加载后运行
onMounted(() => {
  MenuStoreId.initNavMenu()
  getMenuData()
})
</script>

<style lang="scss" scoped>
.nav-menu-box-horizontal {
  display: flex;
  flex-direction: row;
  :deep(.el-menu--horizontal) {
    border-bottom: 0;
  }
  :deep(.el-menu--horizontal>.el-menu-item.is-active) {
    border-bottom: 0;
  }
  :deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title) {
    border-bottom: 0;
  }
}
// .nav-menu {
//   @include background_color("background-color");
// }

.flex-grow {
  flex-grow: 1;
}
.nav-left.hide-nav-left {
  :deep(.el-icon-question:before) {
    margin-right: 6px !important;
  }
}

.nav-left-scroll {
  height: 100%;
}

.logo_box {
  height: 58px;
  cursor: pointer;
  .logo {
    width: 32px;
  }
  .project_name {
    font-size: 15px;
    @include font_color('fontColor');
    min-width: 120px;
    font-family: Arial, sans-serif; /* 使用 Arial 字体 */
    font-weight: bold; /* 字体加粗 */
    text-transform: uppercase; /* 转换为大写字母 */
    letter-spacing: 2px; /* 字间距 */
    text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3); /* 文字阴影 */
    img {
      width: 176px;
      height: 40px;
    }
  }
}

.menu-name-1 {
  display: inline-block;
  max-width: 110px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu-group-name-1 {
  display: inline-block;
  max-width: 145px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu-name-1-1 {
  display: inline-block;
  max-width: 145px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
