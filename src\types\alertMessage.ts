// 预警消息相关类型定义

// 分页查询参数
export interface AlertMessageQueryParams {
  pageNum?: number
  pageSize?: number
  id?: number
  eventId?: string
  modelCode?: string
  modelName?: string
  alertType?: string
  platformName?: string
  period?: string
  serviceNo?: string
  businessType?: string
  state?: string
  indexValue?: string
  reason?: string
  ruleId?: number
  ruleName?: string
  related?: boolean
  warnLevel?: string
  systemCode?: string
  systemName?: string
  configId?: number
  frequency?: number
  warnType?: number
  notificationUsers?: string
  alertTimeStart?: string
  alertTimeEnd?: string
  createTimeStart?: string
  createTimeEnd?: string
  updateTimeStart?: string
  updateTimeEnd?: string
  creator?: string
  updater?: string
  deleted?: boolean
}

// 预警消息页面VO
export interface AlertMessagePageVO {
  id: number
  eventId: string
  modelCode: string
  modelName: string
  alertType: string
  platformName: string
  period: string
  serviceNo: string
  businessType: string
  state: string
  indexValue: string
  reason: string
  payload: string
  ruleId: number
  ruleName: string
  related: boolean
  warnLevel: string
  systemCode: string
  systemName: string
  alertTime: string
  configId: number
  frequency: number
  warnType: number
  notificationUsers: string
  createTime: string
  updateTime: string
  creator: string
  updater: string
  deleted: boolean
}

// 分页响应数据
export interface PageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// API响应格式
export interface ApiResponse<T> {
  code: string
  message: string
  data: T
  timestamp: number
}

// 搜索表单数据
export interface SearchForm {
  query: string
  reportTimeRange: string[]
  thirdPartyName: string
  businessType: string
  systemName: string
  alertType: string
  alertLevel: string
  modelNumber: string
}

// 字典选项数据
export interface DictOption {
  value: string
  label: string
}

// 预警消息导出参数
export interface AlertMessageExportBO {
  ids: number[]
  orderByColumns?: string[]
}

// 预警消息详情VO
export interface AlertMessageDetailVO {
  id: number
  eventId: string
  modelCode: string
  modelName: string
  alertType: string
  platformName: string
  period: string
  serviceNo: string
  businessType: string
  state: string
  indexValue: string
  reason: string
  payload: string
  ruleId: number
  ruleName: string
  related: boolean
  warnLevel: string
  systemCode: string
  systemName: string
  alertTime: string
  configId: number
  frequency: number
  warnType: number
  notificationUsers: string
  createTime: string
  updateTime: string
  creator: string
  updater: string
  deleted: boolean
}
