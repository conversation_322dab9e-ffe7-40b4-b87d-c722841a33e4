<!-- 纵向布局 -->
<template>
  <div>
    <el-container>
      <el-aside class="nav-left" :class="{ 'hide-nav-left': MenuStoreId.isHideFullNavMenu }">
        <section>
          <NavMenu mode="vertical"></NavMenu>
        </section>
      </el-aside>
      <el-main class="main-box">
        <div class="nav-top flex_row flex_column_center">
          <div class="top-left">
            <!-- 菜单展开收起按钮 -->
            <shrink-button></shrink-button>
            <!-- 面包屑 -->
            <right-bread></right-bread>
          </div>
          <div class="top-right flex_row_right flex_column_center">
            <!-- 用户名称 -->
            <user-name></user-name>
            <!-- 满屏操作 -->
            <full-screen v-if="showFullScreen"></full-screen>
            <!-- 主题设置 -->
            <setup-drawer v-if="showSetUp"></setup-drawer>
            <!-- 语言切换 -->
            <lang-select textColor="#303133" v-if="showLang"></lang-select>
          </div>
        </div>
        <div class="panel-tags">
          <TagSelect></TagSelect>
        </div>
        <section class="router-content-box">
          <el-col :span="24">
            <router-view v-slot="{ Component }" v-if="state.isRouterViewAlive">
              <!-- <transition name="fade-transform" mode="out-in"> -->
              <keep-alive :include="TagsStoreId.keepAliveInclude">
                <component :is="Component" :key="router.currentRoute.value.fullPath" />
              </keep-alive>
              <!-- </transition> -->
            </router-view>
          </el-col>
        </section>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { reactive, provide, nextTick } from 'vue'
import NavMenu from '@/components/home/<USER>'
import TagSelect from '@/components/home/<USER>'
import RightBread from '@/components/home/<USER>'
import ShrinkButton from '@/components/home/<USER>'
import UserName from '@/components/home/<USER>'
import FullScreen from '@/components/home/<USER>'
import SetupDrawer from '@/components/home/<USER>'
import LangSelect from '@/components/home/<USER>'
import { menuStore } from '@/stores/menuStore'
import { tagsStore } from '@/stores/tags'
import { globalConfig } from '@/globalSettings'
import { useRouter } from 'vue-router'
const router = useRouter()
// 菜单状态管理
const MenuStoreId = menuStore()
//标签状态管理
const TagsStoreId = tagsStore()
// 全局配置管理
const {
  setting: { showLang, showMessages, showSetUp, showFullScreen }
} = globalConfig
// 状态管理
const state = reactive({
  isRouterViewAlive: true
})
// 刷新二级路由页面
const reloadRouter = async (): Promise<void> => {
  state.isRouterViewAlive = false
  await nextTick()
  state.isRouterViewAlive = true
}
provide('reloadRouter', reloadRouter)
</script>

<style lang="scss" scoped>
.nav-left {
  width: 200px;
  height: 100vh;
  transition: width 300ms;
  background-color: #282c34;
  &.hide-nav-left {
    width: 64px;
  }
  :deep(.el-menu) {
    border-right: none;
  }
  :deep(.el-scrollbar) {
    height: 100%;
  }
  :deep(.el-scrollbar__wrap) {
    height: 100%;
    overflow-x: hidden;
  }
}
.main-box {
  padding: 0;
  .nav-top {
    background-color: #ffffff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    // border-bottom: 1px solid #f9f9f9;
    z-index: 1;
    height: 60px;
    .top-left {
      margin-left: 15px;
    }
    .top-right {
      flex-grow: 1;
    }
  }
  .panel-tags {
    background: #fff;
    box-shadow: 0 3px 5px #ddd;
    padding: 0 15px;
  }
}
.router-content-box {
  position: relative;
  padding: 20px;
  margin: 15px;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  min-height: calc(100vh - 165px);
}
</style>
