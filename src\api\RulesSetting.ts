import { sysApi } from '@/globalSettings'
import http from '@/assets/js/http'

export default {
  // 获取规则列表
  getRulesList: function (params: object) {
    return http.get(`${sysApi}/rules/page`, params)
  },

  // 创建规则
  createRule: function (params: object) {
    return http.post(`${sysApi}/rules/save`, params)
  },

  // 更新规则
  updateRule: function (params: object) {
    return http.post(`${sysApi}/rules/update`, params)
  },

  // 删除规则
  deleteRule: function (id: string) {
    return http.post(`${sysApi}/rules/del/${id}`)
  },

  // 获取规则详情
  getRuleDetail: function (id: string) {
    return http.get(`${sysApi}/rules/info/${id}`)
  },

  // 更新规则状态
  updateRuleStatus: function (id: string) {
    return http.post(`${sysApi}/rules/updateStatus/${id}`)
  }
} 