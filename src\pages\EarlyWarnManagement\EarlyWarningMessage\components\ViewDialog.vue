<!--预警消息查看详情对话框-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="预警消息详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="dialog-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="预警名称">
          {{ dialogForm.alertName }}
        </el-descriptions-item>
        <el-descriptions-item label="预警编号">
          {{ dialogForm.alertCode }}
        </el-descriptions-item>
        <el-descriptions-item label="客户名称">
          {{ dialogForm.customerName }}
        </el-descriptions-item>
        <el-descriptions-item label="客户编号">
          {{ dialogForm.customerCode }}
        </el-descriptions-item>
        <el-descriptions-item label="第三方名称">
          {{ getThirdPartyName(dialogForm.thirdPartyName) }}
        </el-descriptions-item>
        <el-descriptions-item label="业务类型">
          {{ optionDeptVal(dialogForm.businessType, (store() as any).businessTypeOptions) }}
        </el-descriptions-item>
        <el-descriptions-item label="预警类型">
          {{ optionDeptVal(dialogForm.alertType, (store() as any).alertTypeOptions) }}
        </el-descriptions-item>
        <el-descriptions-item label="预警级别">
          <el-tag :type="getAlertLevelType(dialogForm.alertLevel)">
            {{ getAlertLevelText(dialogForm.alertLevel) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="预警规则">
          {{ dialogForm.alertRule }}
        </el-descriptions-item>
        <el-descriptions-item label="直接名称">
          {{ dialogForm.directName }}
        </el-descriptions-item>
        <el-descriptions-item label="限定业务">
          {{ dialogForm.limitedBusiness }}
        </el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag :type="getProcessStatusType(dialogForm.processStatus)">
            {{ getProcessStatusText(dialogForm.processStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="上报时间">
          {{ dialogForm.reportTime }}
        </el-descriptions-item>
        <el-descriptions-item label="处理时间">
          {{ dialogForm.processTime || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="处理人员">
          {{ dialogForm.processor || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ dialogForm.createTime }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 预警详细内容 -->
      <div class="mt20">
        <h4>预警详细内容</h4>
        <el-card class="mt10">
          <div class="alert-content">
            {{ dialogForm.alertContent || '暂无详细内容' }}
          </div>
        </el-card>
      </div>

      <!-- 处理记录 -->
      <div class="mt20" v-if="dialogForm.processRecords && dialogForm.processRecords.length > 0">
        <h4>处理记录</h4>
        <el-timeline class="mt10">
          <el-timeline-item
            v-for="(record, index) in dialogForm.processRecords"
            :key="index"
            :timestamp="record.processTime"
            placement="top"
          >
            <el-card>
              <h5>{{ record.processor }}</h5>
              <p>操作：{{ getProcessActionText(record.action) }}</p>
              <p v-if="record.remark">备注：{{ record.remark }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 相关附件 -->
      <div class="mt20" v-if="dialogForm.attachments && dialogForm.attachments.length > 0">
        <h4>相关附件</h4>
        <div class="attachment-list mt10">
          <el-tag
            v-for="(file, index) in dialogForm.attachments"
            :key="index"
            class="mr10 mb10"
            @click="downloadFile(file)"
            style="cursor: pointer;"
          >
            <el-icon><Document /></el-icon>
            {{ file.fileName }}
          </el-tag>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          type="primary" 
          @click="handleProcess" 
          v-if="dialogForm.processStatus === 'pending'"
        >
          处理
        </el-button>
        <el-button 
          type="warning" 
          @click="handleIgnore" 
          v-if="dialogForm.processStatus === 'pending'"
        >
          忽略
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { store } from '@/stores'
import { optionDeptVal } from '@/assets/js/filters'
import WarnApi from '@/api/EarlyWarnManage'

interface Props {
  dialogVisible: boolean
  dialogForm: any
}

interface Emits {
  (e: 'update:dialogVisible', value: boolean): void
  (e: 'callback'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = computed({
  get: () => props.dialogVisible,
  set: (value) => emit('update:dialogVisible', value)
})

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 获取第三方名称
const getThirdPartyName = (value: string) => {
  const map: Record<string, string> = {
    international: '国际业务',
    domestic: '国内业务'
  }
  return map[value] || value
}

// 获取预警级别类型
const getAlertLevelType = (level: string) => {
  const typeMap: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[level] || 'info'
}

// 获取预警级别文本
const getAlertLevelText = (level: string) => {
  const textMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[level] || level
}

// 获取处理状态类型
const getProcessStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    processed: 'success',
    ignored: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取处理状态文本
const getProcessStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    processed: '已处理',
    ignored: '已忽略'
  }
  return textMap[status] || status
}

// 获取处理动作文本
const getProcessActionText = (action: string) => {
  const textMap: Record<string, string> = {
    process: '处理',
    ignore: '忽略',
    transfer: '转交',
    close: '关闭'
  }
  return textMap[action] || action
}

// 下载文件
const downloadFile = (file: any) => {
  // 这里实现文件下载逻辑
  const link = document.createElement('a')
  link.href = file.fileUrl
  link.download = file.fileName
  link.click()
}

// 处理预警
const handleProcess = () => {
  emit('update:dialogVisible', false)
  // 触发父组件的处理逻辑
  emit('callback')
}

// 忽略预警
const handleIgnore = async () => {
  try {
    await ElMessageBox.confirm('确定要忽略这条预警消息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await WarnApi.ignoreAlertMessage({ id: props.dialogForm.id })
    ElMessage.success('操作成功')
    handleClose()
    emit('callback')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  max-height: 600px;
  overflow-y: auto;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px;
}

.mr10 {
  margin-right: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.alert-content {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
}

.attachment-list {
  display: flex;
  flex-wrap: wrap;
}

h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

h5 {
  margin: 0 0 8px 0;
  color: #409eff;
  font-size: 14px;
  font-weight: 600;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-timeline-item__content) {
  padding-bottom: 15px;
}
</style>
