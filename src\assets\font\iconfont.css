@font-face {
  font-family: 'iconfont'; /* Project id 3875357 */
  src: url('iconfont.woff2?t=1675231650402') format('woff2'),
    url('iconfont.woff?t=1675231650402') format('woff'),
    url('iconfont.ttf?t=1675231650402') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-shuju:before {
  content: '\e61f';
}

.icon-caidan:before {
  content: '\e608';
}

.icon-yewujianmogongcheng:before {
  content: '\e6a3';
}
