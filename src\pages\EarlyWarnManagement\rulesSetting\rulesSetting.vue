<template>
  <div class="rules-setting-container">
    <el-form :inline="true" :model="search" ref="searchFormRef">
      <el-form-item label="规则名称：" prop="ruleName">
        <el-input v-model="search.ruleName" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="规则编号：" prop="ruleCode">
        <el-input v-model="search.ruleCode" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="状态：" prop="ruleStatus">
        <el-select v-model="search.ruleStatus" placeholder="请选择" clearable style="width: 200px;">
          <el-option
            v-for="item in ruleStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button @click="resetForm(searchFormRef)">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button type="primary" @click="handleCreateRule">新建规则</el-button>
    </div>

    <!-- 规则表格 -->
    <div class="table-section">
      <el-table :data="tableData" style="width: 100%" v-loading="loading" border>
        <el-table-column prop="ruleCode" label="规则编号" align="center" width="100" />
        <el-table-column prop="ruleName" label="规则名称" align="center"  />
        <el-table-column prop="applyInvestorName" label="应用资方" align="center"  width="400" />
        <el-table-column prop="creator" label="创建人" align="center"  />
        <el-table-column prop="createTime" label="创建时间" align="center" width="200"  />
        <el-table-column prop="ruleStatus" label="状态" align="center" width="100" >
          <template #default="scope">
            <el-switch
              v-model="scope.row.ruleStatus"
              active-value="0"
              inactive-value="1"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right"  align="center" width="200">
          <template #default="scope">
            <el-button type="primary" link @click="handleView(scope.row)">查看</el-button>
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)" >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting } from '@element-plus/icons-vue'
import RulesSetting from '@/api/RulesSetting'
import { useRouter } from 'vue-router'
const router = useRouter()

// 搜索数据
const search = reactive({
  ruleName: '', // 规则名称
  ruleCode: '', // 规则编号
  ruleStatus: '-1', // 规则状态
})

// 规则状态数据
const ruleStatusOptions = ref([
  { label: '全部', value: '-1' },
  { label: '启用', value: '0' },
  { label: '禁用', value: '1' }
])

// 表格数据
const tableData = ref([])
const loading = ref(false)
// 分页相关
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 获取列表数据
const getList = async (page: number = 1) => {
  loading.value = true
  try {
    const params = {
      ruleName: search.ruleName,
      ruleCode: search.ruleCode,
      ruleStatus: search.ruleStatus,
      pageNum: page || 1,
      pageSize: pagination.pageSize,
      orderFields: 'createTime',
      orderRules: 'desc'
    }
    const response = await RulesSetting.getRulesList(params)
    if (response.code === '0000') {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
      pagination.currentPage = response.data.current
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询规则列表失败:', error)
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = async () => {
  await getList(1)
}

// 新建规则
const handleCreateRule = () => {
  router.push('/rulesDetail')
}

// 查看规则
const handleView = (row: any) => {
  router.push(`/rulesDetail/${row.id}?mode=view`)
}

// 编辑规则
const handleEdit = (row: any) => {
  router.push(`/rulesDetail/${row.id}`)
}

// 删除规则
const handleDelete = (row: any) => {
  ElMessageBox.confirm(
    `确定要删除规则 "${row.ruleName}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
      // 正在生效时，进行二次确认并给出提示
      if (row.ruleStatus === '0') {
        try {
          await ElMessageBox.confirm(
            '该规则正在生效，确定要删除该条规则吗？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
        } catch {
          ElMessage.info('已取消删除')
          return
        }
      }
      try {
        const response = await RulesSetting.deleteRule(row.id)
        if (response.code === '0000') {
          ElMessage.success('删除成功')
          getList(1)
        } else {
          ElMessage.error(response.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除规则失败:', error)
      }
    })
    .catch(() => {
      ElMessage.info('已取消删除')
    })
}

// 状态变更
const handleStatusChange = async (row: any) => {
  try {
    const response = await RulesSetting.updateRuleStatus(row.id)
    if (response.code === '0000') {
      const statusText = row.ruleStatus === '0' ? '启用' : '禁用'
      ElMessage.success(`规则 "${row.ruleName}" 已${statusText}`)
    } else {
      // 如果更新失败，恢复原状态
      row.ruleStatus = row.ruleStatus === '1' ? '0' : '1'
      ElMessage.error(response.msg || '状态更新失败')
    }
  } catch (error) {
    // 如果更新失败，恢复原状态
    row.ruleStatus = row.ruleStatus === '1' ? '0' : '1'
    console.error('更新规则状态失败:', error)
  }
}

// 分页大小变更
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getList(1)
}

// 当前页变更
const handleCurrentChange = (val: number) => {
  getList(val)
}

// 清除过滤条件
const searchFormRef = ref<FormInstance>()
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
  getList(1)
}

// 页面加载
onMounted(() => {
  getList(1)
})
</script>

<style scoped lang="scss">
.rules-setting-container {
  padding: 20px;
  background: #fff;
  height: 100%;
  overflow-y: auto;
}
.action-section {
    margin: 20px 0;
}
.pagination-section {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}
</style>