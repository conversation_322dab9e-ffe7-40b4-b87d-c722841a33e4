<!--配置访问地址弹窗-->
<template>
  <div>
    <el-dialog
      title="访问地址"
      width="500px"
      v-model="state.visible"
      @close="resetForm(formDataRef)"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="state.ruleForm" ref="ruleForm" label-width="100px">
        <el-form-item label="访问地址" prop="accessAddress">
          <el-input
            v-model="state.ruleForm.userAccessUrl"
            style="width: 300px"
            placeholder="请输入jupyter notebook访问地址"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="tc">
        <el-button
          type="primary"
          @click="submitForm(formDataRef)"
          :disabled="state.ruleForm.userAccessUrl === ''"
        >
          确认</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, ref, onMounted } from 'vue'
import { ElMessage, type FormInstance } from 'element-plus'
// import Api from '@/api/UserCenter'
// 参数管理
const props = defineProps({
  addressDialogVisible: {
    type: Boolean,
    default: false
  }
})
// 状态管理
const state = reactive({
  visible: props.addressDialogVisible,
  ruleForm: {
    userAccessUrl: '' // 访问地址
  }
})
// 定义要发送的emit事件
let $emit = defineEmits(['update:addressDialogVisible'])

// 重置表单
const formDataRef = ref<FormInstance>()
const resetForm = (formEl: FormInstance | undefined) => {
  $emit('update:addressDialogVisible', false)
  if (!formEl) return
  formEl.resetFields()
}

// 保存
const submitForm = (formName: FormInstance | undefined) => {
  $emit('update:addressDialogVisible', false)
  if (!formName) return
  formName.validate((valid) => {
    if (valid) {
      $emit('update:addressDialogVisible', false)
      // const params = {
      //   userAccessUrl: state.ruleForm.userAccessUrl
      // }
      // Api.saveUserAccessConf(params).then((res: any) => {
      //   if (res.code === 0) {
      //     ElMessage.success({ message: res.msg })
      //   } else {
      //     ElMessage({ message: res.msg, type: 'error' })
      //   }
      // })
    } else {
      console.log('error submit!')
      return false
    }
  })
}
// const visible = computed({
//   get: () => props.addressDialogVisible,
//   set: value => emits('update:addressDialogVisible', value),
// });

watch(
  () => props.addressDialogVisible,
  (newVal, oldVal) => {
    state.visible = newVal
    console.log('new', newVal)
    console.log('old', oldVal)
  }
)
</script>

<style scoped></style>
