<!-- 字典值列表弹框 -->
<template>
  <div>
    <el-dialog title="字典值列表" v-model="state.visible" width="1200" class="dialog-input" @close="resetDialog()" :close-on-click-modal="false">
      <el-form :inline="true" :model="state.search" ref="dialogForm">
        <el-form-item label="字典项" prop="label">
          <el-input v-model.trim="state.search.label" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="字典值" prop="value">
          <el-input v-model.trim="state.search.value" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getSysDictValuePage(1)">查询</el-button>
        </el-form-item>
      </el-form>
      <el-button class="mb10" type="primary" @click="showAddEditDictValueDialog({})">添加</el-button>
      <el-table :data="state.tableData" border stripe v-loading="loading" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }">
        <el-table-column type="index" width="60" label="序号"/>
        <el-table-column prop="label" label="字典项"/>
        <el-table-column prop="value" label="字典值"/>
        <el-table-column prop="description" label="描述"/>
        <el-table-column prop="createTime" label="创建时间"/>
        <el-table-column prop="updateTime" label="更新时间"/>
        <el-table-column label="操作" width="160">
          <template #default="{ row }">
            <el-button type="primary" text @click="showAddEditDictValueDialog(row)">编辑</el-button>
            <el-button type="primary" text @click="deleteDictValue(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="mt20 flex flex_center">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20, 50, 100]"
          :background="true"
          layout="prev, pager, next, jumper, sizes, total"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <div class="tc mt20">
        <el-button @click="$emit('update:dialogVisible', false)">关 闭</el-button>
      </div>
    </el-dialog>
    <AddEditDictValueDialog v-model:dialogVisible="addEditDictValueDialog.show" :dialogForm="addEditDictValueDialog.row" @callback="addEditDictValueDialog.callback"></AddEditDictValueDialog>
  </div>
</template>

 <script lang="ts" setup>
 import { reactive, watch, ref } from 'vue'
 import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/api/DataManagement'
 import AddEditDictValueDialog from './AddEditDictValueDialog.vue'
 // 定义要发送的emit事件
let $emit = defineEmits(['update:dialogVisible', 'callback'])
 // 参数管理
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  keyId: {
    type: Number,
    default: undefined
  }
})
const state = reactive({
  visible: false,
  search: {
    label: '',
    value: ''
  },
  tableData: [] as any
})

let pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})
const loading = ref(false)
// 获取字典值列表
const getSysDictValuePage = (pageNum:number) => {
  const params = {
    pageNum: pageNum || 1,
    pageSize: pagination.pageSize,
    value: state.search.value,
    label: state.search.label,
    keyId: props.keyId
  }
  loading.value = true
  api.getSysDictValuePage(params).then((res: any) => {
    loading.value = false
    if (res.code === '0000') {
      state.tableData = res.data.records
      pagination.total = res.data.total
      pagination.pageNum = res.data.current
    } else {
      ElMessage({ message: res.message, type: 'error' })
    }
  })
}

const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getSysDictValuePage(1)
}
const handleCurrentChange = (val: number) => {
  pagination.pageNum = val
  getSysDictValuePage(val)
}

// 新增编辑字典弹窗
const addEditDictValueDialog = reactive({
  show: false,
  row: {},
  callback: (data: any) => {
    console.log(data)
  }
})

// 打开新增编辑字典弹窗
const showAddEditDictValueDialog = (row: any) => {
  addEditDictValueDialog.row = row
  row.keyId = props.keyId
  addEditDictValueDialog.show = true
  addEditDictValueDialog.callback = (data: any) => {
    getSysDictValuePage(1)
    console.log(data)
  }
}

// 删除字典值
const deleteDictValue = (row: any) => {
  ElMessageBox.confirm(
  '是否确认删除？', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
  .then(() => {
    api.delSysDictValue({id: row.id}).then((res: any) => {
      if (res.code === '0000') {
        getSysDictValuePage(pagination.pageNum)
        ElMessage({type: 'success', message: '删除成功！'})
      } else {
        ElMessage({type: 'info', message: res.message})
      }
    })
  })
  .catch(() => {
  })
}

// 重置弹窗
const resetDialog = () => {
  state.search.value = ''
  state.search.label = ''
  $emit('update:dialogVisible', false)
}

watch(
  () => props.dialogVisible,
  (newVal) => {
    state.visible = newVal
    if (newVal) {
       getSysDictValuePage(1)
    }
  }
)
</script>

<style lang="scss" scoped>
.el-select__wrapper {
  width: 200px;
}
.dialog-input .el-input {
  width: 260px;
}
.dialog-input ::deep(.el-textarea__inner) {
  width: 260px;
}
.dialog-input .el-select__wrapper {
  width: 260px;
}
.el-message-box__container {
  align-items: normal;
}
.dialog-title {
  margin-bottom: 10px;
  text-align: center;
  font-size: 17px;
  font-weight: bold;
}
</style>