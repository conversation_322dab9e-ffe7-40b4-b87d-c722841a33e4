<!--预警消息处理对话框-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="处理预警消息"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <!-- 预警信息展示 -->
      <el-card class="mb20">
        <template #header>
          <span>预警信息</span>
        </template>
        <el-descriptions :column="1" size="small">
          <el-descriptions-item label="预警名称">
            {{ dialogForm.alertName }}
          </el-descriptions-item>
          <el-descriptions-item label="预警编号">
            {{ dialogForm.alertCode }}
          </el-descriptions-item>
          <el-descriptions-item label="客户名称">
            {{ dialogForm.customerName }}
          </el-descriptions-item>
          <el-descriptions-item label="预警级别">
            <el-tag :type="getAlertLevelType(dialogForm.alertLevel)">
              {{ getAlertLevelText(dialogForm.alertLevel) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 处理表单 -->
      <el-form-item label="处理方式" prop="processAction" required>
        <el-radio-group v-model="form.processAction">
          <el-radio label="process">立即处理</el-radio>
          <el-radio label="transfer">转交他人</el-radio>
          <el-radio label="ignore">忽略处理</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 转交人员选择 -->
      <el-form-item 
        label="转交人员" 
        prop="transferTo" 
        v-if="form.processAction === 'transfer'"
        required
      >
        <el-select 
          v-model="form.transferTo" 
          placeholder="请选择转交人员"
          filterable
          remote
          :remote-method="searchUsers"
          :loading="userLoading"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          />
        </el-select>
      </el-form-item>

      <!-- 处理优先级 -->
      <el-form-item 
        label="处理优先级" 
        prop="priority" 
        v-if="form.processAction === 'process'"
      >
        <el-select v-model="form.priority" placeholder="请选择优先级">
          <el-option label="紧急" value="urgent" />
          <el-option label="高" value="high" />
          <el-option label="中" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>

      <!-- 预计完成时间 -->
      <el-form-item 
        label="预计完成时间" 
        prop="expectedTime" 
        v-if="form.processAction === 'process'"
      >
        <el-date-picker
          v-model="form.expectedTime"
          type="datetime"
          placeholder="请选择预计完成时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <!-- 处理说明 -->
      <el-form-item label="处理说明" prop="remark" required>
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="4"
          :placeholder="getRemarkPlaceholder()"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <!-- 附件上传 -->
      <el-form-item label="相关附件">
        <el-upload
          ref="uploadRef"
          :file-list="fileList"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          :auto-upload="false"
          multiple
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持jpg/png/pdf/doc/docx/xls/xlsx文件，且不超过10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ getSubmitButtonText() }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import WarnApi from '@/api/EarlyWarnManage'

interface Props {
  dialogVisible: boolean
  dialogForm: any
}

interface Emits {
  (e: 'update:dialogVisible', value: boolean): void
  (e: 'callback'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = computed({
  get: () => props.dialogVisible,
  set: (value) => emit('update:dialogVisible', value)
})

// 表单数据
const form = reactive({
  processAction: 'process',
  transferTo: '',
  priority: 'medium',
  expectedTime: '',
  remark: ''
})

// 表单验证规则
const rules = {
  processAction: [
    { required: true, message: '请选择处理方式', trigger: 'change' }
  ],
  transferTo: [
    { required: true, message: '请选择转交人员', trigger: 'change' }
  ],
  remark: [
    { required: true, message: '请输入处理说明', trigger: 'blur' },
    { min: 10, message: '处理说明至少10个字符', trigger: 'blur' }
  ]
}

const formRef = ref()
const uploadRef = ref()
const submitLoading = ref(false)
const userLoading = ref(false)
const userOptions = ref([])
const fileList = ref([])

// 监听处理方式变化，重置相关字段
watch(() => form.processAction, (newVal) => {
  if (newVal !== 'transfer') {
    form.transferTo = ''
  }
  if (newVal !== 'process') {
    form.priority = 'medium'
    form.expectedTime = ''
  }
})

// 获取预警级别类型
const getAlertLevelType = (level: string) => {
  const typeMap: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[level] || 'info'
}

// 获取预警级别文本
const getAlertLevelText = (level: string) => {
  const textMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[level] || level
}

// 获取备注占位符
const getRemarkPlaceholder = () => {
  const placeholderMap: Record<string, string> = {
    process: '请详细说明处理方案和预期结果...',
    transfer: '请说明转交原因和相关背景信息...',
    ignore: '请说明忽略原因和风险评估...'
  }
  return placeholderMap[form.processAction] || '请输入处理说明...'
}

// 获取提交按钮文本
const getSubmitButtonText = () => {
  const textMap: Record<string, string> = {
    process: '开始处理',
    transfer: '确认转交',
    ignore: '确认忽略'
  }
  return textMap[form.processAction] || '确认'
}

// 搜索用户
const searchUsers = async (query: string) => {
  if (!query) {
    userOptions.value = []
    return
  }
  
  try {
    userLoading.value = true
    const res = await WarnApi.searchUsers({ keyword: query })
    if (res.code === '0000') {
      userOptions.value = res.data
    }
  } catch (error) {
    ElMessage.error('搜索用户失败')
  } finally {
    userLoading.value = false
  }
}

// 文件上传相关
const handleFileChange = (file: any, fileList: any[]) => {
  // 文件变化处理
}

const handleFileRemove = (file: any, fileList: any[]) => {
  // 文件移除处理
}

const beforeUpload = (file: any) => {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf', 
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ].includes(file.type)
  
  if (!isValidType) {
    ElMessage.error('文件格式不支持')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  
  return false // 阻止自动上传
}

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const params = {
      id: props.dialogForm.id,
      processAction: form.processAction,
      transferTo: form.transferTo,
      priority: form.priority,
      expectedTime: form.expectedTime,
      remark: form.remark,
      attachments: fileList.value
    }
    
    await WarnApi.processAlertMessage(params)
    ElMessage.success('处理成功')
    handleClose()
    emit('callback')
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('处理失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    processAction: 'process',
    transferTo: '',
    priority: 'medium',
    expectedTime: '',
    remark: ''
  })
  fileList.value = []
  dialogVisible.value = false
}
</script>

<style lang="scss" scoped>
.mb20 {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-upload-dragger) {
  padding: 20px;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.el-radio) {
  margin-right: 0;
}
</style>
