<!--主机详情弹窗-->
<template>
  <div>
    <el-dialog
      title="预警事件详情"
      width="900px"
      v-model="state.visible"
      @close="$emit('update:dialogVisible', false)"
    >
      <table class="detail-table">
        <tr>
          <td class="label">预警事件编号</td>
          <td width="32%">{{props.detailData.eventId}}</td>
          <td class="label">指标值</td>
          <td width="32%">{{props.detailData.indexValue}}</td>
        </tr>
        <tr>
          <td class="label">模型编号</td>
          <td>{{props.detailData.modelCode}}</td>
          <td class="label">原因</td>
          <td>{{props.detailData.reason}}</td>
        </tr>
        <tr>
          <td class="label">预警类型</td>
          <td>{{props.detailData.alertType}}</td>
          <td class="label">拓展</td>
          <td>{{props.detailData.payload}}</td>
        </tr>
        <tr>
          <td class="label">第三方名称</td>
          <td>{{props.detailData.platformName}}</td>
          <td class="label">预警名称</td>
          <td>{{props.detailData.modelName}}</td>
        </tr>
        <tr>
          <td class="label">期次</td>
          <td>{{props.detailData.period}}</td>
          <td class="label">系统名称</td>
          <td>{{props.detailData.systemName}}</td>
        </tr>
        <tr>
          <td class="label">业务唯一编号</td>
          <td>{{props.detailData.serviceNo}}</td>
          <td class="label">预警时间</td>
          <td>{{props.detailData.createTime}}</td>
        </tr>
        <tr>
          <td class="label">业务类型</td>
          <td>{{props.detailData.businessType}}</td>
          <td class="label">是否关联规则</td>
          <td>{{props.detailData.related ? '是' : '否'}}</td>
        </tr>
        <tr>
          <td class="label">状态值</td>
          <td>{{props.detailData.state}}</td>
        </tr>
      </table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue'

// 参数管理
const props = defineProps<{
  dialogVisible: Boolean;
  detailData: any;
}>();

// 定义要发送的emit事件
let $emit = defineEmits(['update:dialogVisible'])

// 状态管理
const state = reactive({
  visible: false
})

watch(
  () => props.dialogVisible,
  async (newVal) => {
    state.visible = newVal
  }
)
</script>

<style lang="scss" scoped>
.detail-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;
  td {
    height: 30px;
    text-align: center;
    padding: 10px 5px;
    border: 1px solid #E4E4E4;
    word-wrap: break-word;
    word-break: break-all;
    &.label {
      background: #F2F2F2;
    }
    .describe {
      height: 100%;
      overflow: auto;
    }
  }
}
</style>
