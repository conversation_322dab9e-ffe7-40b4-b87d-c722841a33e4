// 正则表达式
export default {
  kong: /\S/, // 非空
  phone: /^1\d{10}$/, // 手机号
  number: /^[0-9]*$/, // 只能是数字x
  numberd: /^\d+(\.\d+)?$/, // 只能是数字和小数点
  numberdOrAmount: /^\d+(\.\d{2})?$/, // 限制输入数字，且小数点保留两位
  amount: /^(([1-9][0-9]*)|(([0]|[0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/, // 金额
  area: /^[A-Za-z0-9\u4e00-\u9fa5]+$/, // 文本域：10-200个汉字或字母
  isZh: /^[\u4E00-\u9FA5]{2,4}$/, // 姓名
  contactReg: /^[A-Za-z\u4e00-\u9fa5]+$|[a-z]]/, // 联系人：姓名至少一个汉字或字母
  contractNoReg: /\S/, // 合同编号 匹配由数字和26个英文字母组成的字符串
  nameReg: /^[A-Za-z0-9\u4e00-\u9fa5]+$/, // 名称 至少一个汉字或字母
  busReg: /^[A-Za-z0-9\u4e00-\u9fa5]+$/, // 商家/公司名称 姓名至少一个汉字或字母
  email: /^(\w)+(\.\w+)*@(\w)+((\.\w+)+)$/, // 邮箱号码
  password: /^[\x21-\x7E]{6,20}$/, // 密码
  phoneEmail: /^[1]{1}[0-9]{10}$|(\w)+(\.\w+)*@(\w)+((\.\w+)+)$/, // 匹配手机邮箱
  idCard: /(^\d{15}$)|(^\d{17}([0-9]|X)$)/, // 简单身份证校验
  banknum: /^(\d{16}|\d{18}|\d{19}|\d{20}|\d{21})$/, // 银行卡
  indonesiaPhone: /^\d{8,18}$/, // 印尼手机号 8到18位
  zeroStartNumber: /^0\d*$/, // 0开头数字
  positiveInteger: /^[1-9]\d*$/, // 正整数的正则表达式(不包括0)
  channel: /^ALL|([A-Za-z0-9]+(,[A-Za-z0-9]+)*)$/ // 渠道 只能是数字和英文逗号
}
