<template>
  <div id="h-loginBox">
     <div class="loginBox">
        <el-form
          label-position="left"
          label-width="0px"
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          class="h-loginForm card-box loginform"
        >
          <img src="../assets/imgs/login_logo.png" alt="logo" class="login-logo">
          <p class="login-welcome">欢迎回来！</p>
          <el-form-item prop="account">
            <el-input
              name="account"
              v-model="ruleForm.account"
              auto-complete="off"
              placeholder="请输入账号"
              @keyup.enter="submitForm(ruleFormRef)"
              clearable>
              <template #prepend>
                <img src="../assets/imgs/login_user.png" alt="" class="form-icon">
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password" class="mt25">
            <el-input
              type="password"
              name="password"
              v-model="ruleForm.password"
              auto-complete="off"
              placeholder="请输入密码"
              @keyup.enter="submitForm(ruleFormRef)"
              clearable>
              <template #prepend>
                <img src="../assets/imgs/login_password.png" alt="" class="form-icon">
              </template>
            </el-input>
          </el-form-item>
          <el-checkbox v-model="ruleForm.checked"
                       checked
                       style="margin: 0px 0px 35px 0px; float: left; color: #001D60">
            记住账号
          </el-checkbox>
          <el-form-item class="mt10" style="width: 100%">
            <el-button
              type="primary"
              style="width: 100%"
              @click="submitForm(ruleFormRef)"
              :disabled="ruleForm.loading"
              :loading="ruleForm.loading"
            >登录</el-button
            >
          </el-form-item>
        </el-form>
        <img src="../assets/imgs/login_right.png" alt="" class="login-right-img">
      </div>
    <div id="login_bg"></div>
  </div>
</template>

<script setup lang="ts" name="Login">
import { reactive, onMounted, ref } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useRouter } from 'vue-router'
import User from '../api/User'
import { sysDomainCode } from '@/globalSettings'
import utils from '@/assets/js/utils'
import { Search } from '@element-plus/icons-vue'
let router = useRouter()

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  account: '',
  password: '',
  checked: true,
  loading: false
})
const rules = reactive<FormRules>({
  account: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 18, message: '账号长度在3到18个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 18, message: '密码长度在6到18个字符', trigger: 'blur' }
  ]
})
// 登录表单提交
const submitForm = (formName: FormInstance | undefined) => {
  if (!formName) return
  formName.validate((valid) => {
    if (valid) {
      console.log('submit!')
      ruleForm.loading = true
      const params = {
        domainCode: sysDomainCode,
        username: ruleForm.account,
        password: ruleForm.password
      }
      User.login(params)
        .then((data: any) => {
          const { code, result, msg } = data
          if (code === 0) {
            User.saveInfo(JSON.stringify(result.user)) // 保存用户信息
            utils.setCookie('UserLoginToken', result.token, '720') // 24小时x30天 = 720小时
            if (ruleForm.checked) {
              utils.setCookie('UserLoginName', ruleForm.account, '720') // 记住用户名24小时x30天 = 720小时
            } else {
              utils.deleteCookie('UserLoginName') // 删除用户名
            }
            router.push({ path: '/home' })
          } else {
            ElMessage({
              message: msg,
              type: 'error'
            })
            ruleForm.loading = false
          }
        })
        .catch((error: any) => {
          ruleForm.loading = false
          // this.CodeImg()
          ElMessage(error)
        })
    } else {
      console.log('error submit!')
      return false
    }
  })
}
// 页面加载后执行
onMounted(() => {
  const _name = utils.getCookie('UserLoginName')
  if (_name) {
    ruleForm.account = _name
  }
})
</script>

<style lang="scss" scoped>
.loginBox {
  z-index: 99;
  position: absolute;
  top: 163px;
  left: 23%;
  width: 592px;
  height: 630px;
  background-color: #ffffff;
  box-shadow: 0px 20px 21px 0px rgba(127,141,202,0.16);
}
.card-box {
  width: 336px;
  margin: 0 auto;
  padding-top: 50px;
}

.login-logo {
  display: block;
  width: 174px;
  height: 56px;
}

.login-welcome {
  margin: 50px 0;
  font-size: 32px;
  color: #001D60;
  line-height: 50px;
  letter-spacing: 2px;
}

#h-loginBox .h-loginForm {
  position: relative;
  z-index: 999;
}

.login-right-img {
  z-index: 999;
  position: absolute;
  top: 0;
  left: 96%;
  width: 441px;
  height: 672px;
}

//  表单图标
:deep(.el-input-group__prepend) {
  box-shadow: none;
  background-color: #ffffff;
  padding: 0 15px 0 0;
  &:before {
    content: '';
    position: absolute;
    left: 30px;
    display: block;
    width: 1px;
    height: 18px;
    background: #C5CFDB;
  }
}

:deep(.el-form-item__error) {
  left: 40px;
}

.form-icon {
  position: relative;
  width: 24px;
  height: 24px;
}

:deep(.el-input__wrapper) {
  box-shadow: none;
}

.el-input {
  border-bottom: #E6E6E6 1px solid;
}

.el-button {
  width: 370px;
  height: 50px;
  background: #2370FF;
  border-radius: 10px;
  font-size: 18px;
}

// 背景图
#login_bg {
  width: 100%;
  height: 100%;
  position: fixed;
  background: #000000 url('../assets/imgs/login_bg.png') center center no-repeat;
  background-size: cover;
  z-index: 0;
  left: 0;
  top: 0;
}
</style>
