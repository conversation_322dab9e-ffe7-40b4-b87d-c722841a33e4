{"name": "vue3-vite-ts-pinia", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --mode dev", "test": "vite --mode test", "build:test": "vite build --mode test", "build:pro": "vite build --mode pro", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@better-scroll/core": "^2.5.0", "axios": "^1.3.4", "element-plus": "^2.9.5", "pinia": "^2.0.32", "screenfull": "^6.0.2", "vue": "^3.2.47", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6"}, "devDependencies": {"@rushstack/eslint-patch": "^1.2.0", "@types/node": "^18.14.2", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/tsconfig": "^0.1.3", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.4", "sass": "^1.59.3", "typescript": "~4.8.4", "vite": "^4.1.4", "vue-tsc": "^1.2.0"}}